{"name": "nextjs-scaffold", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "biome check", "lint:fix": "biome lint --write", "lint:fixAll": "biome check --write", "format": "biome format --write"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.74.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "next": "15.5.4", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@tailwindcss/postcss": "^4.1.14", "@types/node": "^20.19.19", "@types/react": "^19.2.0", "@types/react-dom": "^19.2.0", "tailwindcss": "^4.1.14", "tw-animate-css": "^1.4.0", "typescript": "^5.9.3"}}